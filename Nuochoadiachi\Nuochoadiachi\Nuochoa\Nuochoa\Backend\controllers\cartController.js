import userModel from "../models/UserModel.js";
import productModel from "../models/productModels.js";

// Thêm sản phẩm vào giỏ hàng
const addToCart = async (req, res) => {
  try {
    const { userId, itemId, size, quantity, productData } = req.body;

    // Kiểm tra tồn kho trước khi thêm vào giỏ hàng
    const product = await productModel.findById(itemId);
    if (!product) {
      return res.json({ success: false, message: "Sản phẩm không tồn tại" });
    }

    const availableStock = product.quantity || 0;
    if (availableStock <= 0) {
      return res.json({ success: false, message: "Sản phẩm đã hết hàng" });
    }

    let userData = await userModel.findById(userId);
    if (!userData) {
      return res.json({ success: false, message: "Người dùng không tồn tại" });
    }

    let cartData = userData.cartData || [];

    // Tìm sản phẩm đã có trong giỏ hàng
    const existingItemIndex = cartData.findIndex(
      item => item._id === itemId && item.size === size
    );

    const newQuantity = Number(quantity) || 0;
    let totalQuantityAfterAdd = newQuantity;
    if (existingItemIndex !== -1) {
      const currentQty = Number(cartData[existingItemIndex].quantity) || 0;
      totalQuantityAfterAdd = currentQty + newQuantity;
    }

    // Kiểm tra không vượt quá tồn kho
    if (totalQuantityAfterAdd > availableStock) {
      const currentQty = existingItemIndex !== -1 ? Number(cartData[existingItemIndex].quantity) || 0 : 0;
      const maxCanAdd = availableStock - currentQty;
      return res.json({
        success: false,
        message: `Chỉ có thể thêm tối đa ${maxCanAdd} sản phẩm nữa (tồn kho: ${availableStock})`
      });
    }

    if (existingItemIndex !== -1) {
      // Nếu đã có, cộng dồn số lượng
      const currentQty = Number(cartData[existingItemIndex].quantity) || 0;
      cartData[existingItemIndex].quantity = currentQty + newQuantity;
    } else {
      // Nếu chưa có, thêm mới
      cartData.push({
        _id: itemId,
        name: productData.name,
        image: productData.image,
        originalPrice: productData.originalPrice || productData.price,
        price: productData.price,
        hasPromotion: productData.hasPromotion || false,
        promotion: productData.promotion || null,
        discountPercentage: productData.promotion?.discountPercentage || 0,
        size: size,
        quantity: newQuantity,
        availableStock: availableStock // Lưu thông tin tồn kho
      });
    }

    await userModel.findByIdAndUpdate(userId, { cartData });
    res.json({ success: true, message: "Đã thêm vào giỏ hàng" });

  } catch (error) {
    console.log(error);
    res.json({ success: false, message: "Lỗi khi thêm vào giỏ hàng" });
  }
};

// Xóa sản phẩm khỏi giỏ hàng
const removeFromCart = async (req, res) => {
  try {
    const { userId, itemId, size } = req.body;

    let userData = await userModel.findById(userId);
    if (!userData) {
      return res.json({ success: false, message: "Người dùng không tồn tại" });
    }

    let cartData = userData.cartData || [];
    
    // Lọc bỏ sản phẩm cần xóa
    cartData = cartData.filter(item => !(item._id === itemId && item.size === size));

    await userModel.findByIdAndUpdate(userId, { cartData });
    res.json({ success: true, message: "Đã xóa khỏi giỏ hàng" });

  } catch (error) {
    console.log(error);
    res.json({ success: false, message: "Lỗi khi xóa khỏi giỏ hàng" });
  }
};

// Cập nhật số lượng sản phẩm trong giỏ hàng
const updateCartQuantity = async (req, res) => {
  try {
    const { userId, itemId, size, quantity } = req.body;
    const newQuantity = Number(quantity) || 0;

    // Kiểm tra tồn kho nếu quantity > 0
    if (newQuantity > 0) {
      const product = await productModel.findById(itemId);
      if (!product) {
        return res.json({ success: false, message: "Sản phẩm không tồn tại" });
      }

      const availableStock = product.quantity || 0;
      if (newQuantity > availableStock) {
        return res.json({
          success: false,
          message: `Tồn kho chỉ còn ${availableStock} sản phẩm`
        });
      }
    }

    let userData = await userModel.findById(userId);
    if (!userData) {
      return res.json({ success: false, message: "Người dùng không tồn tại" });
    }

    let cartData = userData.cartData || [];

    // Tìm và cập nhật số lượng
    const itemIndex = cartData.findIndex(item => item._id === itemId && item.size === size);

    if (itemIndex !== -1) {
      if (newQuantity <= 0) {
        // Nếu số lượng <= 0, xóa sản phẩm
        cartData.splice(itemIndex, 1);
      } else {
        // Cập nhật số lượng và thông tin tồn kho
        cartData[itemIndex].quantity = newQuantity;
        if (product) {
          cartData[itemIndex].availableStock = product.quantity;
        }
      }
    }

    await userModel.findByIdAndUpdate(userId, { cartData });
    res.json({ success: true, message: "Đã cập nhật giỏ hàng" });

  } catch (error) {
    console.log(error);
    res.json({ success: false, message: "Lỗi khi cập nhật giỏ hàng" });
  }
};

// Lấy giỏ hàng của user
const getCart = async (req, res) => {
  try {
    const { userId } = req.body;

    let userData = await userModel.findById(userId);
    if (!userData) {
      return res.json({ success: false, message: "Người dùng không tồn tại" });
    }

    let cartData = userData.cartData || [];
    res.json({ success: true, cartData });

  } catch (error) {
    console.log(error);
    res.json({ success: false, message: "Lỗi khi lấy giỏ hàng" });
  }
};

// Đồng bộ giỏ hàng từ client lên server
const syncCart = async (req, res) => {
  try {
    const { userId, cartData } = req.body;

    let userData = await userModel.findById(userId);
    if (!userData) {
      return res.json({ success: false, message: "Người dùng không tồn tại" });
    }

    // Cập nhật toàn bộ giỏ hàng
    await userModel.findByIdAndUpdate(userId, { cartData: cartData || [] });
    res.json({ success: true, message: "Đã đồng bộ giỏ hàng" });

  } catch (error) {
    console.log(error);
    res.json({ success: false, message: "Lỗi khi đồng bộ giỏ hàng" });
  }
};

// Xóa toàn bộ giỏ hàng
const clearCart = async (req, res) => {
  try {
    const { userId } = req.body;

    let userData = await userModel.findById(userId);
    if (!userData) {
      return res.json({ success: false, message: "Người dùng không tồn tại" });
    }

    await userModel.findByIdAndUpdate(userId, { cartData: [] });
    res.json({ success: true, message: "Đã xóa toàn bộ giỏ hàng" });

  } catch (error) {
    console.log(error);
    res.json({ success: false, message: "Lỗi khi xóa giỏ hàng" });
  }
};

export { 
  addToCart, 
  removeFromCart, 
  updateCartQuantity, 
  getCart, 
  syncCart, 
  clearCart 
};
