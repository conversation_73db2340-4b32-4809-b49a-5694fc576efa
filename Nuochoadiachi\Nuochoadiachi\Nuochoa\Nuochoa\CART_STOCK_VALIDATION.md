# 🛒 Tính năng Kiểm soát Tồn kho trong Giỏ hàng

## 📋 Tổng quan

Tính năng này đảm bảo việc cộng dồn trong giỏ hàng không được vượt quá số lượng tồn kho, ngăn chặn tình trạng overselling (bán quá số lượng có sẵn).

## ✨ Tính năng chính

### 1. **Kiểm tra Tồn kho khi Thêm vào Giỏ hàng**
- ✅ Kiểm tra sản phẩm còn hàng hay không
- ✅ Kiểm tra số lượng hiện tại trong giỏ hàng
- ✅ Tính toán tổng số lượng sau khi thêm
- ✅ Ngăn chặn thêm vượt quá tồn kho
- ✅ Hiển thị thông báo lỗi rõ ràng

### 2. **Kiểm tra Tồn kho khi Cập nhật Số lượng**
- ✅ Kiểm tra số lượng mới không vượt quá tồn kho
- ✅ Vô hiệu hóa nút tăng khi đạt giới hạn
- ✅ Hiển thị thông tin tồn kho còn lại

### 3. **Đồng bộ Tồn kho Real-time**
- ✅ API endpoint kiểm tra tồn kho: `POST /api/products/stock-check`
- ✅ Hàm `validateCartStock()` để cập nhật thông tin tồn kho
- ✅ Lưu thông tin tồn kho trong giỏ hàng

## 🔧 Cài đặt Kỹ thuật

### Frontend (CartContext.jsx)

```javascript
// Kiểm tra tồn kho trước khi thêm
const addToCart = async (product, size, quantity) => {
  const availableStock = Number(product.quantity) || 0;
  
  // Kiểm tra hết hàng
  if (availableStock <= 0) {
    throw new Error("Sản phẩm đã hết hàng");
  }
  
  // Kiểm tra số lượng trong giỏ hàng
  const currentCartItem = cart.find(p => p._id === product._id && p.size === size);
  const currentQuantityInCart = currentCartItem ? currentCartItem.quantity : 0;
  const totalQuantityAfterAdd = currentQuantityInCart + quantity;
  
  // Kiểm tra không vượt quá tồn kho
  if (totalQuantityAfterAdd > availableStock) {
    const maxCanAdd = availableStock - currentQuantityInCart;
    throw new Error(`Chỉ có thể thêm tối đa ${maxCanAdd} sản phẩm nữa (tồn kho: ${availableStock})`);
  }
  
  // Thêm vào giỏ hàng...
};
```

### Backend (cartController.js)

```javascript
// Kiểm tra tồn kho trước khi thêm vào giỏ hàng
const addToCart = async (req, res) => {
  const { itemId, quantity } = req.body;
  
  // Kiểm tra tồn kho
  const product = await productModel.findById(itemId);
  if (!product || product.quantity <= 0) {
    return res.json({ success: false, message: "Sản phẩm đã hết hàng" });
  }
  
  // Kiểm tra số lượng trong giỏ hàng hiện tại
  const existingItem = cartData.find(item => item._id === itemId && item.size === size);
  const totalQuantityAfterAdd = (existingItem?.quantity || 0) + quantity;
  
  if (totalQuantityAfterAdd > product.quantity) {
    const maxCanAdd = product.quantity - (existingItem?.quantity || 0);
    return res.json({ 
      success: false, 
      message: `Chỉ có thể thêm tối đa ${maxCanAdd} sản phẩm nữa (tồn kho: ${product.quantity})` 
    });
  }
  
  // Thêm vào giỏ hàng...
};
```

### API Endpoint mới

```javascript
// GET /api/products/stock-check
export const checkStock = async (req, res) => {
  const { productIds } = req.body;
  
  const products = await productModel.find(
    { _id: { $in: productIds } },
    { _id: 1, name: 1, quantity: 1 }
  );
  
  res.json({ success: true, products });
};
```

## 🎨 Giao diện Người dùng

### 1. **Thông báo Lỗi**
- Toast notification khi vượt quá tồn kho
- Thông báo rõ ràng số lượng tối đa có thể thêm

### 2. **Hiển thị Tồn kho trong Giỏ hàng**
- Hiển thị "Còn lại: X" dưới mỗi sản phẩm
- Vô hiệu hóa nút "+" khi đạt giới hạn tồn kho
- Vô hiệu hóa nút "-" khi số lượng = 1

### 3. **Trạng thái Hết hàng**
- Badge "Hết hàng" trên sản phẩm
- Vô hiệu hóa nút "Thêm vào giỏ hàng"

## 🧪 Testing

Chạy test để kiểm tra tính năng:

```bash
npm run test cart-stock-validation.test.js
```

### Test Cases:
- ✅ Thêm sản phẩm khi còn hàng
- ✅ Ngăn chặn thêm khi vượt quá tồn kho
- ✅ Ngăn chặn thêm sản phẩm hết hàng
- ✅ Cập nhật số lượng trong giới hạn tồn kho
- ✅ Ngăn chặn cập nhật vượt quá tồn kho

## 📱 Trải nghiệm Người dùng

### Khi thêm sản phẩm:
1. **Thành công**: Hiển thị toast "🛒 Đã thêm vào giỏ hàng"
2. **Hết hàng**: Hiển thị toast "❌ Sản phẩm đã hết hàng"
3. **Vượt tồn kho**: Hiển thị toast "❌ Chỉ có thể thêm tối đa X sản phẩm nữa"

### Trong giỏ hàng:
1. **Hiển thị tồn kho**: "Còn lại: 5" dưới mỗi sản phẩm
2. **Nút bị vô hiệu hóa**: Khi đạt giới hạn tồn kho
3. **Thông báo lỗi**: Khi cố gắng tăng quá giới hạn

## 🔄 Luồng Hoạt động

```mermaid
graph TD
    A[User clicks Add to Cart] --> B{Check if product exists}
    B -->|No| C[Show error: Product not found]
    B -->|Yes| D{Check if in stock}
    D -->|No| E[Show error: Out of stock]
    D -->|Yes| F[Get current quantity in cart]
    F --> G{Check total quantity <= stock}
    G -->|No| H[Show error: Exceeds stock limit]
    G -->|Yes| I[Add to cart successfully]
    I --> J[Show success message]
    I --> K[Navigate to cart page]
```

## 🚀 Lợi ích

1. **Ngăn chặn Overselling**: Không bán quá số lượng có sẵn
2. **Trải nghiệm tốt hơn**: Thông báo rõ ràng cho người dùng
3. **Quản lý tồn kho chính xác**: Đồng bộ real-time
4. **Giảm lỗi đặt hàng**: Kiểm tra trước khi thanh toán

## 🔧 Cấu hình

Trong `productModels.js`:
```javascript
const productSchema = new mongoose.Schema({
  // ...
  quantity: { type: Number, default: 0 }, // Số lượng tồn kho
  minStock: { type: Number, default: 5 }, // Mức tồn kho tối thiểu
  // ...
});
```

## 📝 Ghi chú

- Tính năng này hoạt động cho cả người dùng đã đăng nhập và chưa đăng nhập
- Thông tin tồn kho được cập nhật real-time từ database
- Hỗ trợ nhiều size sản phẩm với cùng một tồn kho chung
- Tương thích với hệ thống khuyến mãi hiện có
