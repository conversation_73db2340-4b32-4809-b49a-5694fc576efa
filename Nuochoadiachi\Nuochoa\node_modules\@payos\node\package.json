{"name": "@payos/node", "version": "1.0.10", "description": "", "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib"], "prepare": "npm run build", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "tsc"}, "keywords": ["payos", "casso", "vietqr", "vietqrpro", "vietnam qrpay"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.5.0", "crypto": "^1.0.1"}, "devDependencies": {"@types/node": "^20.5.9", "typescript": "^5.2.2"}, "repository": {"type": "git", "url": "https://github.com/payOSHQ/payos-lib-node.git"}}