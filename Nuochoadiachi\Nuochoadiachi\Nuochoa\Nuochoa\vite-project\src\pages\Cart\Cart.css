.cart-wrapper {
  max-width: 1100px;
  margin: 40px auto;
  padding: 20px;
  font-family: 'Inter', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  color: #2c3e50;
  line-height: 1.6;
}

/* Cart Header */
.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 12px;
  border: 1px solid #dee2e6;
}

.cart-title-section {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.cart-title {
  margin: 0;
  color: #2c3e50;
  font-size: 1.8em;
  font-weight: 700;
}

.user-info {
  margin: 0;
  color: #667eea;
  font-size: 0.9em;
  font-weight: 600;
}

.cart-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.cart-count {
  background: #667eea;
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.85em;
  font-weight: 600;
}

.clear-cart-btn {
  background: #e74c3c;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9em;
  font-weight: 600;
  transition: all 0.3s ease;
}

.clear-cart-btn:hover {
  background: #c0392b;
  transform: translateY(-1px);
}

/* Empty Cart */
.cart-empty {
  text-align: center;
  padding: 60px 20px;
  background: #f8f9fa;
  border-radius: 12px;
  margin: 20px 0;
}

.empty-icon {
  font-size: 4em;
  margin-bottom: 20px;
  opacity: 0.5;
}

.cart-empty h3 {
  color: #4a5568;
  margin-bottom: 10px;
  font-size: 1.5em;
}

.cart-empty p {
  color: #718096;
  margin-bottom: 20px;
}

.cart-empty .user-info {
  color: #667eea;
  font-size: 1em;
  margin-bottom: 30px;
}

.continue-shopping {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1em;
  font-weight: 600;
  transition: all 0.3s ease;
}

.continue-shopping:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.empty-msg {
  font-size: 16px;
  color: #666;
  text-align: center;
  margin-top: 40px;
}

/* ==== Bố cục chia 2 bên ==== */
.cart-layout {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

/* ==== Bảng giỏ hàng ==== */
.cart-table {
  width: 100%;
  border-collapse: collapse;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 0 8px rgba(0,0,0,0.03);
}

.cart-table th {
  background-color: #f9f9f9;
  text-align: left;
  padding: 16px;
  font-weight: 600;
  color: #2c3e50;
  font-size: 15px;
  border-bottom: 1px solid #eee;
  letter-spacing: 0.3px;
}

.cart-table td {
  padding: 16px;
  border-bottom: 1px solid #eee;
  vertical-align: middle;
  font-size: 15px;
  color: #2c3e50;
  font-weight: 400;
}

/* Cart image with promotion badge */
.cart-image-cell {
  position: relative;
}

.image-container {
  position: relative;
  display: inline-block;
}

.cart-img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
}

.cart-badge {
  position: absolute;
  top: -5px;
  left: -5px;
  font-size: 10px;
  padding: 2px 6px;
}

/* Product name with promotion info */
.product-name {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.promotion-info {
  margin-top: 5px;
}

.promotion-tag {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 0.75em;
  font-weight: 600;
}

/* Price cell with promotion */
.price-cell {
  min-width: 120px;
}

.price-with-promotion {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.original-price {
  text-decoration: line-through;
  color: #999;
  font-size: 0.9em;
}

.discounted-price {
  color: #e74c3c;
  font-weight: bold;
  font-size: 1.1em;
}

.normal-price {
  color: #2c3e50;
  font-weight: 600;
}

/* Subtotal cell with promotion */
.subtotal-cell {
  min-width: 150px;
}

.subtotal-with-promotion {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.original-subtotal {
  text-decoration: line-through;
  color: #999;
  font-size: 0.9em;
}

.discounted-subtotal {
  color: #e74c3c;
  font-weight: bold;
  font-size: 1.1em;
}

.savings {
  color: #27ae60;
  font-size: 0.8em;
  font-weight: 600;
  background: #d5f4e6;
  padding: 2px 6px;
  border-radius: 4px;
  margin-top: 2px;
}

.normal-subtotal {
  color: #2c3e50;
  font-weight: 600;
}

/* ==== Điều chỉnh số lượng ==== */
.quantity-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.quantity-controls > div:first-child {
  display: flex;
  align-items: center;
  gap: 8px;
}

.quantity-controls button {
  background-color: #fff;
  border: 1px solid #ccc;
  padding: 5px 10px;
  cursor: pointer;
  border-radius: 4px;
  font-size: 14px;
  transition: 0.2s;
  min-width: 30px;
}

.quantity-controls button:hover:not(:disabled) {
  background-color: #f3f3f3;
}

.quantity-controls button:disabled {
  background-color: #f5f5f5;
  color: #ccc;
  cursor: not-allowed;
}

.stock-info {
  font-size: 11px;
  color: #666;
  text-align: center;
  margin-top: 2px;
}

.stock-info small {
  background-color: #f8f9fa;
  padding: 2px 6px;
  border-radius: 3px;
  border: 1px solid #e9ecef;
}

/* ==== Nút xoá ==== */
.delete-btn {
  background: transparent;
  border: none;
  color: #e74c3c;
  font-size: 18px;
  cursor: pointer;
  transition: transform 0.2s;
}

.delete-btn:hover {
  transform: scale(1.2);
}

/* ==== Tổng kết đơn hàng ==== */
.cart-summary-box {
  max-width: 380px;
  margin-left: auto;
  border: 1px solid #eee;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.04);
  background-color: #fff;
}

.cart-summary-box h3 {
  font-size: 18px;
  margin-bottom: 20px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin: 8px 0;
  font-size: 15px;
  color: #444;
}

.summary-item.total {
  font-weight: bold;
  font-size: 16px;
  color: #000;
  margin-top: 12px;
  border-top: 1px solid #ddd;
  padding-top: 10px;
}

/* Promotion summary styles */
.summary-item.original-total {
  color: #999;
  text-decoration: line-through;
  font-size: 0.9em;
}

.summary-item.savings-total {
  color: #27ae60;
  font-weight: 600;
  background: #d5f4e6;
  padding: 8px 15px;
  border-radius: 6px;
  margin: 5px 0;
}

.savings-amount {
  color: #27ae60;
  font-weight: bold;
  font-size: 1.1em;
}

.promotion-summary {
  background: linear-gradient(135deg, #fff5f5, #fed7d7);
  border: 2px solid #fc8181;
  border-radius: 8px;
  padding: 15px;
  margin: 15px 0;
  text-align: center;
}

.promotion-note {
  color: #c53030;
  font-weight: 600;
  font-size: 0.95em;
}

.checkout-btn {
  margin-top: 20px;
  width: 180px; /* nhỏ lại */
  background-color: #000; /* màu đen */
  border: none;
  color: #fff;
  padding: 12px;
  font-size: 15px;
  font-weight: 600;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  letter-spacing: 0.3px;
}

.checkout-btn:hover {
  background-color: #333; /* hover xám đậm */
}


/* ==== Responsive ==== */
@media (max-width: 768px) {
  .cart-header {
    flex-direction: column;
    gap: 15px;
    padding: 15px;
    text-align: center;
  }

  .cart-title-section {
    align-items: center;
  }

  .cart-title {
    font-size: 1.5em;
  }

  .cart-actions {
    justify-content: center;
    gap: 10px;
  }

  .clear-cart-btn {
    padding: 6px 12px;
    font-size: 0.8em;
  }

  .cart-table th, .cart-table td {
    font-size: 13px;
    padding: 10px;
  }

  .cart-summary-box {
    width: 100%;
  }

  .cart-layout {
    flex-direction: column;
  }
}
