// Test script để kiểm tra tính năng kiểm soát tồn kho trong giỏ hàng
// Chạy: node test-cart-stock.js

console.log('🧪 Testing Cart Stock Validation...\n');

// Test 1: Kiểm tra cộng dồn số với string và number
function testNumberAddition() {
  console.log('📝 Test 1: Number Addition');
  
  // Trường hợp lỗi (string + number)
  let quantity1 = "2"; // String từ input
  let quantity2 = 3;   // Number
  let wrongResult = quantity1 + quantity2; // "23" (string concatenation)
  console.log(`❌ Wrong: "${quantity1}" + ${quantity2} = "${wrongResult}" (type: ${typeof wrongResult})`);
  
  // Trường hợp đúng (convert to number first)
  let correctResult = Number(quantity1) + quantity2; // 5 (number addition)
  console.log(`✅ Correct: Number("${quantity1}") + ${quantity2} = ${correctResult} (type: ${typeof correctResult})`);
  
  console.log('');
}

// Test 2: Kiểm tra logic tồn kho
function testStockValidation() {
  console.log('📝 Test 2: Stock Validation Logic');
  
  const product = {
    _id: 'product1',
    name: 'Test Product',
    quantity: 10 // Tồn kho: 10
  };
  
  const cart = [
    { _id: 'product1', size: '500ml', quantity: 3 }
  ];
  
  // Thêm 2 sản phẩm nữa
  const newQuantity = 2;
  const currentCartItem = cart.find(p => p._id === product._id && p.size === '500ml');
  const currentQuantityInCart = currentCartItem ? Number(currentCartItem.quantity) : 0;
  const totalQuantityAfterAdd = currentQuantityInCart + newQuantity;
  
  console.log(`Current in cart: ${currentQuantityInCart}`);
  console.log(`Adding: ${newQuantity}`);
  console.log(`Total after add: ${totalQuantityAfterAdd}`);
  console.log(`Available stock: ${product.quantity}`);
  
  if (totalQuantityAfterAdd <= product.quantity) {
    console.log('✅ Can add to cart');
  } else {
    const maxCanAdd = product.quantity - currentQuantityInCart;
    console.log(`❌ Cannot add. Max can add: ${maxCanAdd}`);
  }
  
  console.log('');
}

// Test 3: Kiểm tra trường hợp vượt quá tồn kho
function testExceedStock() {
  console.log('📝 Test 3: Exceed Stock Scenario');
  
  const product = {
    _id: 'product1',
    name: 'Test Product',
    quantity: 5 // Tồn kho: 5
  };
  
  const cart = [
    { _id: 'product1', size: '500ml', quantity: 4 }
  ];
  
  // Cố gắng thêm 3 sản phẩm nữa (tổng: 7, tồn kho: 5)
  const newQuantity = 3;
  const currentCartItem = cart.find(p => p._id === product._id && p.size === '500ml');
  const currentQuantityInCart = currentCartItem ? Number(currentCartItem.quantity) : 0;
  const totalQuantityAfterAdd = currentQuantityInCart + newQuantity;
  
  console.log(`Current in cart: ${currentQuantityInCart}`);
  console.log(`Trying to add: ${newQuantity}`);
  console.log(`Total would be: ${totalQuantityAfterAdd}`);
  console.log(`Available stock: ${product.quantity}`);
  
  if (totalQuantityAfterAdd > product.quantity) {
    const maxCanAdd = product.quantity - currentQuantityInCart;
    console.log(`❌ Cannot add ${newQuantity}. Max can add: ${maxCanAdd}`);
    console.log(`Error message: "Chỉ có thể thêm tối đa ${maxCanAdd} sản phẩm nữa (tồn kho: ${product.quantity})"`);
  } else {
    console.log('✅ Can add to cart');
  }
  
  console.log('');
}

// Test 4: Kiểm tra hết hàng
function testOutOfStock() {
  console.log('📝 Test 4: Out of Stock Scenario');
  
  const product = {
    _id: 'product1',
    name: 'Test Product',
    quantity: 0 // Hết hàng
  };
  
  const newQuantity = 1;
  
  console.log(`Trying to add: ${newQuantity}`);
  console.log(`Available stock: ${product.quantity}`);
  
  if (product.quantity <= 0) {
    console.log('❌ Cannot add. Product is out of stock');
    console.log('Error message: "Sản phẩm đã hết hàng"');
  } else {
    console.log('✅ Can add to cart');
  }
  
  console.log('');
}

// Test 5: Kiểm tra update quantity
function testUpdateQuantity() {
  console.log('📝 Test 5: Update Quantity Validation');
  
  const cartItem = {
    _id: 'product1',
    size: '500ml',
    quantity: 2,
    availableStock: 5
  };
  
  const newQuantity = 6; // Vượt quá tồn kho
  
  console.log(`Current quantity: ${cartItem.quantity}`);
  console.log(`Trying to update to: ${newQuantity}`);
  console.log(`Available stock: ${cartItem.availableStock}`);
  
  if (newQuantity > cartItem.availableStock) {
    console.log(`❌ Cannot update. Stock limit: ${cartItem.availableStock}`);
    console.log(`Error message: "Tồn kho chỉ còn ${cartItem.availableStock} sản phẩm"`);
  } else {
    console.log('✅ Can update quantity');
  }
  
  console.log('');
}

// Chạy tất cả tests
testNumberAddition();
testStockValidation();
testExceedStock();
testOutOfStock();
testUpdateQuantity();

console.log('🎉 All tests completed!');
console.log('\n📋 Summary:');
console.log('- ✅ Fixed number addition issue (string + number)');
console.log('- ✅ Added stock validation before adding to cart');
console.log('- ✅ Added stock validation before updating quantity');
console.log('- ✅ Added proper error messages');
console.log('- ✅ Added stock info display in cart');
console.log('- ✅ Disabled buttons when stock limit reached');
