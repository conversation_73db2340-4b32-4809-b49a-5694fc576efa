/* Modern Sidebar.css */

.sidebar-container {
  width: 280px;
  background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
  border-right: 1px solid rgba(0,0,0,0.08);
  height: 100vh;
  padding: 24px 20px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  position: relative;
  overflow-y: auto;
}

.sidebar-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #f55a2c, #ff8c42);
}

.sidebar-title {
  font-size: 20px;
  font-weight: 700;
  background: linear-gradient(135deg, #f55a2c, #ff8c42);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 32px;
  text-align: center;
  letter-spacing: 1px;
}

.sidebar-menu {
  list-style: none;
  padding: 0;
  margin: 0;
  flex: 1;
}

.sidebar-menu li {
  margin-bottom: 8px;
}

.sidebar-menu li a {
  text-decoration: none;
  color: #64748b;
  font-size: 15px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 14px;
  padding: 14px 16px;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.sidebar-menu li a::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #f55a2c, #ff8c42);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.sidebar-menu li a:hover {
  color: #f55a2c;
  background: rgba(245, 90, 44, 0.08);
  transform: translateX(4px);
}

.sidebar-menu li.active a {
  background: linear-gradient(135deg, #f55a2c, #ff8c42);
  color: white;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(245, 90, 44, 0.3);
}

.sidebar-menu li.active a::before {
  opacity: 1;
}

.sidebar-menu i {
  width: 20px;
  text-align: center;
  font-size: 16px;
}

/* Admin Info */
.admin-info {
  background: linear-gradient(135deg, rgba(245, 90, 44, 0.05), rgba(255, 140, 66, 0.05));
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  border: 1px solid rgba(245, 90, 44, 0.1);
  backdrop-filter: blur(10px);
}

.admin-avatar {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #f55a2c, #ff8c42);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  box-shadow: 0 4px 15px rgba(245, 90, 44, 0.3);
}

.admin-details {
  flex: 1;
}

.admin-name {
  font-weight: 600;
  color: #2d3748;
  font-size: 15px;
  margin-bottom: 4px;
}

.admin-email {
  font-size: 13px;
  color: #718096;
}

/* Sidebar Footer */
.sidebar-footer {
  margin-top: auto;
  padding-top: 24px;
  border-top: 1px solid rgba(0,0,0,0.08);
  flex-shrink: 0;
}

.logout-btn {
  width: 100%;
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
  border: none;
  padding: 14px 16px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.logout-btn:hover {
  background: linear-gradient(135deg, #c82333, #a71e2a);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

.logout-btn i {
  font-size: 16px;
}

/* Responsive */
@media (max-width: 768px) {
  .sidebar-container {
    width: 100%;
    height: auto;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .sidebar-container.open {
    transform: translateX(0);
  }
}
