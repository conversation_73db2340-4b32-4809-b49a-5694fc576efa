import React, { useState } from 'react';
import { useCart } from '../../context/CartContext';
import { toast } from 'react-toastify';

const CartStockTest = () => {
  const { cart, addToCart, updateQuantity } = useCart();
  const [testResults, setTestResults] = useState([]);

  // Mock product data for testing
  const mockProduct = {
    _id: 'test-product-1',
    name: 'Test Product',
    price: 100000,
    quantity: 5, // Tồn kho: 5 sản phẩm
    sizes: ['500ml'],
    image: 'test.jpg',
    brand: 'Test Brand',
    description: 'Test product for stock validation'
  };

  const addTestResult = (test, result, expected, actual) => {
    const success = result === expected;
    setTestResults(prev => [...prev, {
      test,
      success,
      expected,
      actual,
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  const runTest1 = async () => {
    try {
      console.log('🧪 Test 1: Add product within stock limit');
      await addToCart(mockProduct, '500ml', 3);
      addTestResult('Add 3 products (stock: 5)', 'success', 'success', 'success');
      toast.success('✅ Test 1 passed: Added 3 products successfully');
    } catch (error) {
      addTestResult('Add 3 products (stock: 5)', 'error', 'success', error.message);
      toast.error('❌ Test 1 failed: ' + error.message);
    }
  };

  const runTest2 = async () => {
    try {
      console.log('🧪 Test 2: Try to add more than stock limit');
      await addToCart(mockProduct, '500ml', 3); // Đã có 3, thêm 3 nữa = 6 > 5
      addTestResult('Add 3 more products (total would be 6, stock: 5)', 'success', 'error', 'success');
      toast.error('❌ Test 2 failed: Should have thrown error');
    } catch (error) {
      addTestResult('Add 3 more products (total would be 6, stock: 5)', 'error', 'error', error.message);
      toast.success('✅ Test 2 passed: Correctly prevented overselling');
    }
  };

  const runTest3 = async () => {
    try {
      console.log('🧪 Test 3: Update quantity within stock limit');
      const cartItem = cart.find(item => item._id === mockProduct._id);
      if (cartItem) {
        await updateQuantity(cartItem, 4); // Cập nhật thành 4 (trong giới hạn 5)
        addTestResult('Update quantity to 4 (stock: 5)', 'success', 'success', 'success');
        toast.success('✅ Test 3 passed: Updated quantity successfully');
      } else {
        addTestResult('Update quantity to 4 (stock: 5)', 'error', 'success', 'No item in cart');
        toast.error('❌ Test 3 failed: No item in cart');
      }
    } catch (error) {
      addTestResult('Update quantity to 4 (stock: 5)', 'error', 'success', error.message);
      toast.error('❌ Test 3 failed: ' + error.message);
    }
  };

  const runTest4 = async () => {
    try {
      console.log('🧪 Test 4: Try to update quantity beyond stock limit');
      const cartItem = cart.find(item => item._id === mockProduct._id);
      if (cartItem) {
        await updateQuantity(cartItem, 6); // Cập nhật thành 6 (vượt quá 5)
        addTestResult('Update quantity to 6 (stock: 5)', 'success', 'error', 'success');
        toast.error('❌ Test 4 failed: Should have thrown error');
      } else {
        addTestResult('Update quantity to 6 (stock: 5)', 'error', 'error', 'No item in cart');
        toast.success('✅ Test 4 passed: No item to update');
      }
    } catch (error) {
      addTestResult('Update quantity to 6 (stock: 5)', 'error', 'error', error.message);
      toast.success('✅ Test 4 passed: Correctly prevented quantity update');
    }
  };

  const runAllTests = async () => {
    setTestResults([]);
    await runTest1();
    setTimeout(() => runTest2(), 1000);
    setTimeout(() => runTest3(), 2000);
    setTimeout(() => runTest4(), 3000);
  };

  const clearCart = () => {
    // Manually clear cart for testing
    setTestResults([]);
  };

  return (
    <div style={{ padding: '20px', border: '1px solid #ccc', margin: '20px', borderRadius: '8px' }}>
      <h3>🧪 Cart Stock Validation Test</h3>
      
      <div style={{ marginBottom: '20px' }}>
        <h4>Mock Product:</h4>
        <p>Name: {mockProduct.name}</p>
        <p>Stock: {mockProduct.quantity}</p>
        <p>Price: {mockProduct.price.toLocaleString()} đ</p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h4>Current Cart:</h4>
        {cart.length === 0 ? (
          <p>Cart is empty</p>
        ) : (
          cart.map((item, index) => (
            <div key={index} style={{ padding: '5px', border: '1px solid #eee', margin: '5px 0' }}>
              <p>Product: {item.name}</p>
              <p>Quantity: {item.quantity} (Type: {typeof item.quantity})</p>
              <p>Available Stock: {item.availableStock}</p>
            </div>
          ))
        )}
      </div>

      <div style={{ marginBottom: '20px' }}>
        <button onClick={runTest1} style={{ margin: '5px', padding: '10px' }}>
          Test 1: Add 3 products
        </button>
        <button onClick={runTest2} style={{ margin: '5px', padding: '10px' }}>
          Test 2: Add 3 more (should fail)
        </button>
        <button onClick={runTest3} style={{ margin: '5px', padding: '10px' }}>
          Test 3: Update to 4
        </button>
        <button onClick={runTest4} style={{ margin: '5px', padding: '10px' }}>
          Test 4: Update to 6 (should fail)
        </button>
        <button onClick={runAllTests} style={{ margin: '5px', padding: '10px', backgroundColor: '#007bff', color: 'white' }}>
          Run All Tests
        </button>
        <button onClick={clearCart} style={{ margin: '5px', padding: '10px', backgroundColor: '#dc3545', color: 'white' }}>
          Clear Results
        </button>
      </div>

      <div>
        <h4>Test Results:</h4>
        {testResults.length === 0 ? (
          <p>No tests run yet</p>
        ) : (
          testResults.map((result, index) => (
            <div 
              key={index} 
              style={{ 
                padding: '10px', 
                margin: '5px 0', 
                backgroundColor: result.success ? '#d4edda' : '#f8d7da',
                border: `1px solid ${result.success ? '#c3e6cb' : '#f5c6cb'}`,
                borderRadius: '4px'
              }}
            >
              <p><strong>{result.success ? '✅' : '❌'} {result.test}</strong></p>
              <p>Expected: {result.expected}</p>
              <p>Actual: {result.actual}</p>
              <p>Time: {result.timestamp}</p>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default CartStockTest;
