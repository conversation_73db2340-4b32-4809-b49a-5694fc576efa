import userModel from "../models/UserModel.js";
import jwt from "jsonwebtoken"
import bcrypt from "bcrypt"
import validator from "validator"

// Đ<PERSON>ng nhập người dùng
const loginUser = async (req, res) => {
  const { email, password } = req.body;
  try {
    const user = await userModel.findOne({ email });

    if (!user) {
      return res.json({ success: false, message: "Người dùng không tồn tại" });
    }

    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.json({ success: false, message: "Sai mật khẩu" });
    }

    const token = createToken(user._id);
    res.json({
      success: true,
      token,
      userId: user._id,
      name: user.name,
      email: user.email
    });

  } catch (error) {
    console.log(error);
    res.json({ success: false, message: "Lỗi máy chủ khi đăng nhập" });
  }
}

const createToken = (id) => {
  return jwt.sign({ id }, process.env.JWT_SECRET);
}

// Đăng ký người dùng
const registerUser = async (req, res) => {
  const { name, password, email } = req.body;
  try {
    const exists = await userModel.findOne({ email });
    if (exists) {
      return res.json({ success: false, message: "Người dùng đã tồn tại" });
    }

    if (!validator.isEmail(email)) {
      return res.json({ success: false, message: "Vui lòng nhập địa chỉ email hợp lệ" });
    }

    if (password.length < 8) {
      return res.json({ success: false, message: "Vui lòng nhập mật khẩu đủ mạnh (tối thiểu 8 ký tự)" });
    }

    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    const newUser = new userModel({
      name: name,
      email: email,
      password: hashedPassword
    });

    const user = await newUser.save();
    res.json({
      success: true,
      message: "Đăng ký thành công! Vui lòng đăng nhập để tiếp tục."
    });

  } catch (error) {
    console.log(error);
    res.json({ success: false, message: "Lỗi máy chủ khi đăng ký" });
  }
}

export { loginUser, registerUser };
