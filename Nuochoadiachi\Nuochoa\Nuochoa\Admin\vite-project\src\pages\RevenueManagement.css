.revenue-management {
  padding: 24px;
  background: transparent;
  min-height: 100vh;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.revenue-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  background: white;
  padding: 32px;
  border-radius: 20px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  border: 1px solid rgba(255,255,255,0.2);
}

.revenue-header h1 {
  margin: 0;
  font-size: 32px;
  font-weight: 700;
  background: linear-gradient(135deg, #f55a2c, #ff8c42);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.refresh-btn {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.refresh-btn:hover {
  background: linear-gradient(135deg, #20c997, #17a2b8);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.date-filter .date-select {
  padding: 10px 15px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  transition: border-color 0.3s;
}

.date-filter .date-select:focus {
  outline: none;
  border-color: #f55a2c;
}

/* Revenue Stats */
.revenue-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 15px;
  transition: transform 0.2s, box-shadow 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.stat-icon {
  font-size: 40px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: #f8f9fa;
}

.stat-card.total .stat-icon {
  background: linear-gradient(135deg, #f55a2c, #ff8c42);
  color: white;
}

.stat-card.today .stat-icon {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
}

.stat-card.orders .stat-icon {
  background: linear-gradient(135deg, #17a2b8, #6f42c1);
  color: white;
}

.stat-card.average .stat-icon {
  background: linear-gradient(135deg, #ffc107, #fd7e14);
  color: white;
}

.stat-info h3 {
  margin: 0 0 5px 0;
  color: #666;
  font-size: 14px;
  font-weight: 500;
}

.stat-value {
  margin: 0;
  color: #333;
  font-size: 24px;
  font-weight: 700;
}

/* Charts */
.charts-container {
  margin-bottom: 30px;
}

/* Professional Chart Section */
.professional-chart-section {
  margin-bottom: 32px;
}

.modern-revenue-chart {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid rgba(245, 90, 44, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.modern-revenue-chart:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 2px solid #f1f5f9;
}

.chart-title h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 700;
  color: #1e293b;
  background: linear-gradient(135deg, #f55a2c, #ff8c42);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.chart-subtitle {
  margin: 0;
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.chart-controls {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 12px;
}

.chart-legend {
  display: flex;
  gap: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  font-weight: 500;
  color: #475569;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 3px;
}

.legend-color.revenue {
  background: linear-gradient(135deg, #f55a2c, #ff8c42);
}

.legend-color.orders {
  background: linear-gradient(135deg, #28a745, #20c997);
}

.chart-content {
  padding: 0 8px;
  position: relative;
  overflow: hidden;
}

.chart-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(245, 90, 44, 0.02) 0%,
    rgba(255, 140, 66, 0.02) 50%,
    rgba(255, 215, 0, 0.02) 100%);
  pointer-events: none;
  border-radius: 12px;
}

/* Chart Animation */
.modern-revenue-chart {
  animation: slideInUp 0.8s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Chart Hover Effects */
.chart-content:hover {
  background: rgba(255, 255, 255, 0.05);
  transition: background 0.3s ease;
}

.revenue-summary-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 30px;
}

.summary-card.compact-daily {
  background: linear-gradient(135deg, #fff 0%, #f0f9ff 100%);
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.summary-card {
  background: white;
  padding: 28px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  border: 1px solid rgba(255,255,255,0.2);
  transition: transform 0.2s, box-shadow 0.2s;
}

.summary-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.summary-card.highlight {
  background: linear-gradient(135deg, #fff 0%, #f8f9ff 100%);
  border: 1px solid rgba(245, 90, 44, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #f1f3f4;
}

.card-header h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  color: #2d3748;
}

.period-badge, .total-badge {
  background: linear-gradient(135deg, #f55a2c, #ff8c42);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.daily-revenue-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.daily-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-radius: 12px;
  background: #f8f9fa;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.daily-item.has-revenue {
  background: linear-gradient(135deg, #fff5f2 0%, #fff 100%);
  border: 2px solid #f55a2c20;
  transform: scale(1.02);
  box-shadow: 0 4px 15px rgba(245, 90, 44, 0.1);
}

.daily-item.no-revenue {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  opacity: 0.7;
}

.date-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.date {
  font-size: 16px;
  font-weight: 700;
  color: #2d3748;
}

.day-name {
  font-size: 12px;
  color: #718096;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.revenue-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.revenue-amount {
  font-size: 16px;
  font-weight: 700;
  color: #f55a2c;
}

.daily-item.no-revenue .revenue-amount {
  color: #a0aec0;
}

.orders-count {
  font-size: 12px;
  color: #718096;
}

.revenue-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: #e2e8f0;
  border-radius: 0 0 12px 12px;
}

.revenue-fill {
  height: 100%;
  background: linear-gradient(90deg, #f55a2c, #ff8c42);
  border-radius: 0 0 12px 12px;
  transition: width 0.5s ease;
}

.status-breakdown {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.status-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.status-row:hover {
  background: #e9ecef;
  transform: translateX(4px);
}

.status-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-name {
  font-weight: 600;
  color: #2d3748;
  font-size: 14px;
}

.status-stats {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.status-count {
  font-size: 14px;
  font-weight: 700;
  color: #4a5568;
}

.status-amount {
  font-size: 12px;
  color: #f55a2c;
  font-weight: 600;
}

.chart-card {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.chart-card h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

/* Status Distribution */
.status-distribution {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.status-summary {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.status-summary h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.status-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  transition: background-color 0.2s;
}

.status-item:hover {
  background: #e9ecef;
}

.status-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
}

.status-name {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.status-count {
  font-size: 12px;
  color: #666;
}

.status-amount {
  font-size: 13px;
  color: #f55a2c;
  font-weight: 600;
}

/* Loading */
.revenue-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: #666;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #f55a2c;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Additional Charts */
.additional-charts {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 30px;
}

.trend-analysis {
  background: linear-gradient(135deg, #fff 0%, #faf5ff 100%);
  border: 1px solid rgba(139, 92, 246, 0.1);
}

.performance-metrics {
  background: linear-gradient(135deg, #fff 0%, #f0fdf4 100%);
  border: 1px solid rgba(34, 197, 94, 0.1);
}

.trend-indicators {
  display: flex;
  gap: 12px;
}

.trend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.trend-item.positive {
  background: linear-gradient(135deg, #dcfce7, #bbf7d0);
  color: #166534;
}

.trend-icon {
  font-size: 14px;
}

.metrics-grid {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.metric-item:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.metric-item.highlight-metric {
  background: linear-gradient(135deg, #fff5f2 0%, #fff 100%);
  border: 2px solid rgba(245, 90, 44, 0.2);
}

.metric-item.highlight-metric:hover {
  border-color: rgba(245, 90, 44, 0.4);
  box-shadow: 0 8px 25px rgba(245, 90, 44, 0.15);
}

.metric-icon {
  font-size: 24px;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.metric-icon.best-day {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  animation: pulse 2s infinite;
}

.metric-icon.total-orders {
  background: linear-gradient(135deg, #3b82f6, #60a5fa);
  color: white;
}

.metric-icon.conversion {
  background: linear-gradient(135deg, #10b981, #34d399);
  color: white;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.metric-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.metric-label {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metric-value {
  font-size: 18px;
  font-weight: 700;
  color: #1e293b;
}

.metric-amount {
  font-size: 12px;
  color: #f55a2c;
  font-weight: 600;
}

/* Metric Enhancements */
.metric-trend {
  position: absolute;
  top: 12px;
  right: 12px;
  font-size: 16px;
}

.metric-trend.up {
  color: #10b981;
}

.metric-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.progress-bar {
  width: 100%;
  height: 100%;
  background: #e2e8f0;
  border-radius: 0 0 12px 12px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #60a5fa);
  transition: width 1s ease;
  animation: progressFill 2s ease-out;
}

@keyframes progressFill {
  from { width: 0%; }
  to { width: var(--progress-width, 0%); }
}

.metric-chart {
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 60px;
  height: 20px;
}

.mini-chart {
  display: flex;
  align-items: end;
  gap: 2px;
  height: 100%;
}

.mini-bar {
  flex: 1;
  min-height: 2px;
  border-radius: 1px;
  transition: all 0.3s ease;
}

.mini-bar.has-revenue {
  background: linear-gradient(to top, #f55a2c, #ff8c42);
}

.mini-bar.no-revenue {
  background: #e2e8f0;
}

/* Responsive */
@media (max-width: 1200px) {
  .revenue-summary-cards {
    grid-template-columns: 1fr;
  }

  .status-distribution {
    grid-template-columns: 1fr;
  }

  .additional-charts {
    grid-template-columns: 1fr;
  }

  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .chart-controls {
    align-items: flex-start;
  }

  .chart-legend {
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .revenue-management {
    padding: 15px;
  }

  .revenue-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .revenue-header h1 {
    font-size: 24px;
  }

  .revenue-stats {
    grid-template-columns: 1fr;
  }

  .stat-card {
    padding: 20px;
  }

  .stat-icon {
    font-size: 32px;
    width: 50px;
    height: 50px;
  }

  .stat-value {
    font-size: 20px;
  }

  .chart-card {
    padding: 20px;
  }

  .chart-card h3, .chart-title h3 {
    font-size: 16px;
  }

  .chart-subtitle {
    font-size: 12px;
  }

  .chart-legend {
    gap: 12px;
  }

  .legend-item {
    font-size: 11px;
  }

  .metrics-grid {
    gap: 16px;
  }

  .metric-item {
    padding: 16px;
    position: relative;
  }

  .metric-icon {
    font-size: 20px;
    width: 40px;
    height: 40px;
  }

  .metric-value {
    font-size: 16px;
  }

  .metric-trend {
    position: static;
    margin-left: auto;
  }

  .metric-chart {
    position: static;
    width: 100%;
    height: 16px;
    margin-top: 8px;
  }

  .mini-chart {
    gap: 1px;
  }
}

@media (max-width: 480px) {
  .revenue-stats {
    gap: 15px;
  }

  .stat-card {
    padding: 15px;
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }

  .stat-info h3 {
    font-size: 12px;
  }

  .stat-value {
    font-size: 18px;
  }

  .chart-header {
    padding: 16px;
  }

  .chart-title h3 {
    font-size: 14px;
  }

  .chart-subtitle {
    font-size: 11px;
  }

  .chart-legend {
    flex-direction: column;
    gap: 8px;
  }

  .period-badge, .total-badge {
    padding: 4px 8px;
    font-size: 10px;
  }

  .metric-item {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .metric-info {
    align-items: center;
  }
}
