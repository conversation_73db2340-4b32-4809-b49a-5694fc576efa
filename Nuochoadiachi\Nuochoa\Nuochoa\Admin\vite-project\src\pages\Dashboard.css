.modern-dashboard {
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  position: relative;
}

.modern-dashboard::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

.modern-dashboard > * {
  position: relative;
  z-index: 1;
}

/* Loading State */
.dashboard-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  color: #666;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #f55a2c;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Header Section */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  gap: 20px;
}

.dashboard-header:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.header-content {
  flex: 1;
  min-width: 300px;
}

.header-content h1 {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 8px 0;
  color: #1f2937;
}

.header-content p {
  color: #718096;
  font-size: 16px;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 16px;
  flex-shrink: 0;
  z-index: 10;
  position: relative;
  min-width: 300px;
  justify-content: flex-end;
}

.action-btn {
  padding: 12px 24px;
  border-radius: 12px;
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  white-space: nowrap;
  position: relative;
  z-index: 10;
  min-width: 140px;
  text-align: center;
}

.action-btn.primary {
  background: linear-gradient(135deg, #f55a2c, #ff8c42);
  color: white;
  box-shadow: 0 4px 15px rgba(245, 90, 44, 0.3);
}

.action-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(245, 90, 44, 0.4);
}

.action-btn.secondary {
  background: white;
  color: #f55a2c;
  border: 2px solid #f55a2c;
}

.action-btn.secondary:hover {
  background: #f55a2c;
  color: white;
  transform: translateY(-2px);
}

/* Admin Stats Grid */
.admin-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.admin-stat-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 16px;
  min-height: 120px;
}

.admin-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #d1d5db;
}

.admin-stat-card .stat-icon {
  font-size: 32px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  border-radius: 12px;
  flex-shrink: 0;
}

.admin-stat-card .stat-content {
  flex: 1;
}

.admin-stat-card .stat-title {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-stat-card .stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
  line-height: 1.2;
}

.admin-stat-card .stat-subtitle {
  font-size: 12px;
  color: #9ca3af;
  font-weight: 500;
}

/* Charts Section */
.charts-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

.chart-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow:
    0 8px 32px rgba(0,0,0,0.12),
    0 2px 8px rgba(0,0,0,0.08),
    inset 0 1px 0 rgba(255,255,255,0.5);
  overflow: hidden;
  border: 1px solid rgba(255,255,255,0.3);
  transition: all 0.3s ease;
}

.chart-card:hover {
  transform: translateY(-4px);
  box-shadow:
    0 12px 40px rgba(0,0,0,0.15),
    0 4px 12px rgba(0,0,0,0.1),
    inset 0 1px 0 rgba(255,255,255,0.6);
}

.chart-card.large {
  grid-column: span 1;
}

.chart-header {
  padding: 24px 24px 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255,255,255,0.1);
  margin: -1px -1px 24px -1px;
  border-radius: 24px 24px 0 0;
}

.chart-header h3 {
  font-size: 20px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

.chart-legend {
  display: flex;
  gap: 16px;
}

.legend-item {
  font-size: 12px;
  font-weight: 500;
}

.legend-item.revenue {
  color: #f55a2c;
}

.legend-item.orders {
  color: #28a745;
}

/* Bottom Charts */
.bottom-charts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
}

/* Order Summary */
.order-summary {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  padding: 32px;
  border-radius: 24px;
  box-shadow:
    0 8px 32px rgba(0,0,0,0.12),
    0 2px 8px rgba(0,0,0,0.08),
    inset 0 1px 0 rgba(255,255,255,0.5);
  border: 1px solid rgba(255,255,255,0.3);
  transition: all 0.3s ease;
}

.order-summary:hover {
  transform: translateY(-4px);
  box-shadow:
    0 12px 40px rgba(0,0,0,0.15),
    0 4px 12px rgba(0,0,0,0.1),
    inset 0 1px 0 rgba(255,255,255,0.6);
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 0;
  border-bottom: 1px solid #e2e8f0;
}

.summary-item:last-of-type {
  border-bottom: none;
}

.summary-icon {
  width: 64px;
  height: 64px;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  box-shadow:
    0 6px 20px rgba(0,0,0,0.12),
    0 2px 6px rgba(0,0,0,0.08),
    inset 0 1px 0 rgba(255,255,255,0.2);
  transition: all 0.3s ease;
  position: relative;
}

.summary-icon::before {
  content: '';
  position: absolute;
  inset: 2px;
  border-radius: 16px;
  background: linear-gradient(135deg, rgba(255,255,255,0.3), transparent);
  pointer-events: none;
}

.summary-icon.pending {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
  color: white;
}

.summary-icon.processing {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  color: white;
}

.summary-icon.shipped {
  background: linear-gradient(135deg, #fa709a, #fee140);
  color: white;
}

.summary-icon.completed {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.summary-content h4 {
  font-size: 14px;
  color: #718096;
  margin: 0 0 4px 0;
  font-weight: 500;
}

.summary-content p {
  font-size: 24px;
  font-weight: 700;
  color: #2d3748;
  margin: 0;
}

.summary-total {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 2px solid #e2e8f0;
  text-align: center;
}

.summary-total h3 {
  font-size: 18px;
  color: #2d3748;
  margin: 0 0 16px 0;
}

.view-all-btn {
  background: linear-gradient(135deg, #f55a2c, #ff8c42);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.view-all-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(245, 90, 44, 0.3);
}

/* Responsive */
@media (max-width: 1200px) {
  .charts-section {
    grid-template-columns: 1fr;
  }

  .dashboard-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .header-actions {
    justify-content: center;
    min-width: auto;
  }
}

@media (max-width: 768px) {
  .modern-dashboard {
    padding: 16px;
  }

  .dashboard-header {
    flex-direction: column;
    gap: 20px;
    text-align: center;
    padding: 24px;
    min-height: auto;
  }

  .header-content {
    min-width: auto;
    width: 100%;
  }

  .header-actions {
    flex-direction: column;
    width: 100%;
    gap: 12px;
    min-width: auto;
    justify-content: center;
  }

  .action-btn {
    width: 100%;
    padding: 14px 20px;
    font-size: 16px;
  }

  .header-content h1 {
    font-size: 28px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .stat-card {
    padding: 24px;
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .stat-icon {
    width: 64px;
    height: 64px;
    font-size: 36px;
  }

  .stat-value {
    font-size: 28px;
  }

  .charts-section {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .bottom-charts {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .chart-header h3 {
    font-size: 16px;
  }
}

/* Simple animations removed for cleaner look */
}

/* Modern Stats Grid */
.modern-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.modern-stat-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 32px;
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
}

.modern-stat-card:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.card-icon {
  position: absolute;
  top: 24px;
  right: 24px;
  z-index: 2;
}

.icon-wrapper {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.revenue-icon {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(99, 102, 241, 0.2));
  color: #8b5cf6;
}

.orders-icon {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.2), rgba(16, 185, 129, 0.2));
  color: #22c55e;
}

.products-icon {
  background: linear-gradient(135deg, rgba(251, 146, 60, 0.2), rgba(249, 115, 22, 0.2));
  color: #fb923c;
}

.today-icon {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.2), rgba(5, 150, 105, 0.2));
  color: #059669;
}

.users-icon {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.2), rgba(79, 70, 229, 0.2));
  color: #6366f1;
}

.card-content {
  position: relative;
  z-index: 2;
}

.card-content h3 {
  font-size: 12px;
  font-weight: 700;
  letter-spacing: 1.5px;
  color: #64748b;
  margin-bottom: 12px;
  text-transform: uppercase;
}

.modern-stat-card .stat-value {
  font-size: 42px;
  font-weight: 800;
  color: #1e293b;
  margin-bottom: 8px;
  line-height: 1;
  background: linear-gradient(135deg, #1e293b, #475569);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-subtitle {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.card-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.05;
  z-index: 1;
  transition: opacity 0.3s ease;
}

.modern-stat-card:hover .card-gradient {
  opacity: 0.1;
}

.revenue-gradient {
  background: linear-gradient(135deg, #8b5cf6, #6366f1);
}

.orders-gradient {
  background: linear-gradient(135deg, #22c55e, #10b981);
}

.products-gradient {
  background: linear-gradient(135deg, #fb923c, #f97316);
}

.today-gradient {
  background: linear-gradient(135deg, #059669, #047857);
}

.users-gradient {
  background: linear-gradient(135deg, #6366f1, #4f46e5);
}

/* Card Special Styling */
.revenue-card {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.05), rgba(99, 102, 241, 0.05));
  border-left: 4px solid #8b5cf6;
}

.orders-card {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.05), rgba(16, 185, 129, 0.05));
  border-left: 4px solid #22c55e;
}

.products-card {
  background: linear-gradient(135deg, rgba(251, 146, 60, 0.05), rgba(249, 115, 22, 0.05));
  border-left: 4px solid #fb923c;
}

.today-revenue-card {
  background: linear-gradient(135deg, rgba(5, 150, 105, 0.05), rgba(4, 120, 87, 0.05));
  border-left: 4px solid #059669;
}

.users-card {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.05), rgba(79, 70, 229, 0.05));
  border-left: 4px solid #6366f1;
}

/* Responsive for Modern Cards */
@media (max-width: 768px) {
  .modern-stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .modern-stat-card {
    padding: 24px;
  }

  .modern-stat-card .stat-value {
    font-size: 32px;
  }

  .icon-wrapper {
    width: 56px;
    height: 56px;
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  .modern-stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .modern-stat-card {
    padding: 20px;
  }

  .modern-stat-card .stat-value {
    font-size: 28px;
  }

  .card-content h3 {
    font-size: 11px;
  }

  .stat-subtitle {
    font-size: 12px;
  }

  .icon-wrapper {
    width: 48px;
    height: 48px;
    font-size: 20px;
  }
}

/* Dashboard Charts Section */
.dashboard-charts-section {
  margin-top: 40px;
  margin-bottom: 40px;
}

.charts-title {
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 32px;
  text-align: center;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stats-charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.chart-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.12);
  border: 1px solid rgba(255,255,255,0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.chart-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0,0,0,0.15);
}

.chart-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  border-radius: 20px 20px 0 0;
}

.revenue-chart::before {
  background: linear-gradient(90deg, #8b5cf6, #a855f7);
}

.orders-chart::before {
  background: linear-gradient(90deg, #22c55e, #16a34a);
}

.products-chart::before {
  background: linear-gradient(90deg, #fb923c, #f97316);
}

.users-chart::before {
  background: linear-gradient(90deg, #6366f1, #4f46e5);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(255,255,255,0.2);
}

.chart-header h3 {
  font-size: 18px;
  font-weight: 700;
  color: #2d3748;
  margin: 0;
}

.chart-legend {
  display: flex;
  gap: 16px;
}

.legend-item {
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-item.revenue {
  color: #8b5cf6;
}

.legend-item.orders {
  color: #22c55e;
}

.legend-item.products {
  color: #fb923c;
}

.legend-item.users {
  color: #6366f1;
}

/* Chart Responsive */
@media (max-width: 1200px) {
  .stats-charts-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .stats-charts-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .chart-card {
    padding: 20px;
  }

  .chart-header h3 {
    font-size: 16px;
  }

  .charts-title {
    font-size: 24px;
    margin-bottom: 24px;
  }
}

@media (max-width: 480px) {
  .chart-card {
    padding: 16px;
  }

  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .chart-legend {
    gap: 12px;
  }

  .legend-item {
    font-size: 11px;
  }
}

/* Tạm thời tắt floating animation để tránh lỗi layout */
/* .dashboard-header {
  animation: float 6s ease-in-out infinite;
} */
