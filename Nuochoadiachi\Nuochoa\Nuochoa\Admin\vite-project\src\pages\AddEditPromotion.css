.add-edit-promotion {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #e2e8f0;
}

.page-header h1 {
  color: #2d3748;
  font-size: 2em;
  margin: 0;
}

.back-btn {
  background: #e2e8f0;
  color: #4a5568;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: #cbd5e0;
}

/* Loading */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 20px;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Form */
.promotion-form {
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  padding: 30px;
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-section h3 {
  color: #2d3748;
  font-size: 1.3em;
  margin: 0 0 10px 0;
  padding-bottom: 10px;
  border-bottom: 2px solid #e2e8f0;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.form-group label {
  font-weight: 600;
  color: #4a5568;
  font-size: 0.9em;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 12px;
  border: 2px solid #e2e8f0;
  border-radius: 6px;
  font-size: 1em;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error {
  border-color: #e53e3e;
}

.form-group small {
  color: #718096;
  font-size: 0.8em;
  font-style: italic;
}

.error-message {
  color: #e53e3e;
  font-size: 0.8em;
  font-weight: 500;
}

/* Product Selection */
.product-selection {
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 20px;
  background: #f8f9fa;
}

.select-all-option {
  margin-bottom: 20px;
  padding: 15px;
  background: white;
  border-radius: 6px;
  border: 2px solid #e2e8f0;
}

.select-all-option label {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  font-weight: 600;
  color: #2d3748;
}

.select-all-option input[type="checkbox"] {
  width: 18px;
  height: 18px;
}

.selected-products,
.available-products {
  margin-bottom: 20px;
}

.selected-products h4,
.available-products h4 {
  color: #2d3748;
  margin-bottom: 15px;
  font-size: 1.1em;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
  padding: 10px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background: white;
}

.product-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  transition: all 0.3s ease;
  position: relative;
}

.product-item:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.product-item.selected {
  border-color: #48bb78;
  background: #f0fff4;
}

.product-item img {
  width: 50px;
  height: 50px;
  object-fit: cover;
  border-radius: 6px;
  margin-bottom: 8px;
}

.product-info {
  text-align: center;
  flex: 1;
}

.product-info h5 {
  margin: 0 0 5px 0;
  font-size: 0.9em;
  color: #2d3748;
  line-height: 1.3;
}

.product-info p {
  margin: 0;
  color: #667eea;
  font-weight: 600;
  font-size: 0.8em;
}

.add-btn,
.remove-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 25px;
  height: 25px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-weight: bold;
  font-size: 0.9em;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.add-btn {
  background: #48bb78;
  color: white;
}

.add-btn:hover {
  background: #38a169;
  transform: scale(1.1);
}

.remove-btn {
  background: #e53e3e;
  color: white;
}

.remove-btn:hover {
  background: #c53030;
  transform: scale(1.1);
}

/* Form Actions */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  padding: 20px 30px;
  background: #f8f9fa;
  border-top: 1px solid #e2e8f0;
}

.cancel-btn,
.submit-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1em;
}

.cancel-btn {
  background: #e2e8f0;
  color: #4a5568;
}

.cancel-btn:hover {
  background: #cbd5e0;
}

.submit-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Responsive */
@media (max-width: 768px) {
  .add-edit-promotion {
    padding: 15px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .page-header h1 {
    font-size: 1.5em;
    text-align: center;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 20px;
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: 10px;
  }
  
  .product-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 10px;
  }
  
  .product-item {
    padding: 10px;
  }
  
  .product-item img {
    width: 50px;
    height: 50px;
  }
  
  .form-actions {
    flex-direction: column;
    padding: 15px 20px;
  }
  
  .cancel-btn,
  .submit-btn {
    width: 100%;
  }
}
