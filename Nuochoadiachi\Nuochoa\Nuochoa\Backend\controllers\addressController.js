import User from '../models/User.js';
import mongoose from 'mongoose';

export const addAddress = async (req, res) => {
  try {
    const { userId, ...address } = req.body;

    // Ki<PERSON><PERSON> tra user có tồn tại không
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ success: false, message: "Không tìm thấy người dùng" });
    }

    // ✅ Chỉ update address, không động đến phần còn lại
    const updatedUser = await User.findByIdAndUpdate(
      userId,
      { $push: { address: address } },
      { new: true } // Trả về user đã update
    );

    return res.json({
      success: true,
      message: "Thêm địa chỉ thành công",
      addressList: updatedUser.address
    });
  } catch (err) {
    console.error("❌ Lỗi khi thêm địa chỉ:", err);
    return res.status(500).json({ success: false, message: err.message });
  }
};







export const getAddressList = async (req, res) => {
  try {
    const { userId } = req.params;

    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return res.status(400).json({ success: false, message: 'userId không hợp lệ' });
    }

    const user = await User.findById(userId).select('address');
    if (!user) {
      return res.status(404).json({ success: false, message: 'Không tìm thấy người dùng.' });
    }

    return res.status(200).json({ success: true, addressList: user.address || [] });
  } catch (error) {
    console.error("Lỗi khi lấy danh sách địa chỉ:", error);
    return res.status(500).json({ success: false, message: 'Có lỗi xảy ra.' });
  }
};


export const deleteAddress = async (req, res) => {
  const { userId, addressId } = req.params;

  try {
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ success: false, message: "Người dùng không tồn tại" });
    }

    const result = await User.updateOne(
      { _id: userId },
      { $pull: { address: { _id: addressId } } }
    );

    if (result.modifiedCount > 0) {
      res.json({ success: true, message: "Địa chỉ đã được xoá" });
    } else {
      res.status(404).json({ success: false, message: "Không tìm thấy địa chỉ để xoá" });
    }
  } catch (error) {
    console.error("❌ Lỗi khi xoá địa chỉ:", error);
    res.status(500).json({ success: false, message: "Lỗi server khi xoá địa chỉ" });
  }
};



