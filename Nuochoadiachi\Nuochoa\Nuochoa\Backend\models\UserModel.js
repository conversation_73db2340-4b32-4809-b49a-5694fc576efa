import mongoose from "mongoose";

const userSchema = new mongoose.Schema({
  name: { type: String, required: true },
  email: { type: String, required: true },
  password: { type: String, required: true },
  cartData: {
    type: Array,
    default: [],
    // Cấu trúc: [{ _id, name, image, originalPrice, price, hasPromotion, promotion, size, quantity }]
  },
  createdAt: { type: Date, default: Date.now },
  isActive: { type: Boolean, default: true }
}, { minimize: false, timestamps: true });

const userModel = mongoose.models.user || mongoose.model("user", userSchema);
export default userModel;
