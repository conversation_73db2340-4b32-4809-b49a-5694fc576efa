// middleware/auth.js - Middleware thông minh tự động phát hiện loại token
import authJWT from './authJWT.js';
import authFirebase from './authFirebase.js';

const auth = async (req, res, next) => {
  console.log("🔍 Auth middleware called for:", req.method, req.path);
  const { token } = req.headers;

  if (!token) {
    console.log("❌ No token provided");
    return res.json({ success: false, message: "Không được phép truy cập. Vui lòng đăng nhập lại" });
  }

  console.log("🔍 Token received:", token.substring(0, 50) + "...");
  console.log("🔍 Token length:", token.length);
  console.log("🔍 Token parts:", token.split('.').length);

  // Phát hiện loại token dựa trên đặc điểm
  // Firebase token thường dài hơn 800 ký tự và có 3 phần (header.payload.signature)
  const isFirebaseToken = token.length > 800 && token.split('.').length === 3;

  // Kiểm tra thêm: Firebase token thường bắt đầu với eyJhbGciOiJSUzI1NiI
  const hasFirebaseHeader = token.startsWith('eyJhbGciOiJSUzI1NiI');

  if (isFirebaseToken || hasFirebaseHeader) {
    console.log("🔥 Detected Firebase token, using Firebase auth");

    // Try Firebase auth first, fallback to simple auth if Firebase fails
    try {
      return await authFirebase(req, res, next);
    } catch (error) {
      console.log("⚠️ Firebase auth failed, trying simple auth fallback");
      return simpleAuthFallback(req, res, next, token);
    }
  } else {
    console.log("🔑 Detected JWT token, using JWT auth");
    return authJWT(req, res, next);
  }
};

// Simple auth fallback for Firebase tokens
const simpleAuthFallback = (req, res, next, token) => {
  try {
    // Decode Firebase token manually to get user info
    const parts = token.split('.');
    const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());

    console.log("🔄 Using simple auth fallback for user:", payload.user_id);

    // Set user info
    if (!req.body) {
      req.body = {};
    }

    req.body.userId = payload.user_id;
    req.user = {
      uid: payload.user_id,
      id: payload.user_id,
      userId: payload.user_id,
      email: payload.email || null,
      name: payload.name || null
    };

    console.log("✅ Simple auth fallback successful for user:", payload.user_id);
    next();
  } catch (error) {
    console.log("❌ Simple auth fallback failed:", error.message);
    res.json({ success: false, message: "Token không hợp lệ" });
  }
};

export default auth;
