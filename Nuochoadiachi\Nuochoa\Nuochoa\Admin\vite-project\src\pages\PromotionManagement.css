.promotion-management {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #e2e8f0;
}

.page-header h1 {
  color: #2d3748;
  font-size: 2em;
  margin: 0;
}

.add-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  text-decoration: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  display: inline-block;
}

.add-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* Stats Cards */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
  border-left: 4px solid #667eea;
}

.stat-card h3 {
  font-size: 2em;
  margin: 0 0 5px 0;
  color: #667eea;
}

.stat-card p {
  margin: 0;
  color: #4a5568;
  font-weight: 500;
}

/* Loading và Error */
.loading, .error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 20px;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.retry-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.retry-btn:hover {
  background: #5a67d8;
}

/* Table */
.promotions-table-container {
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.promotions-table {
  width: 100%;
  border-collapse: collapse;
}

.promotions-table th {
  background: #f8f9fa;
  padding: 15px 12px;
  text-align: left;
  font-weight: 600;
  color: #4a5568;
  border-bottom: 2px solid #e2e8f0;
}

.promotions-table td {
  padding: 15px 12px;
  border-bottom: 1px solid #e2e8f0;
  vertical-align: top;
}

.promotions-table tr:hover {
  background: #f8f9fa;
}

.promotion-title strong {
  display: block;
  color: #2d3748;
  margin-bottom: 4px;
}

.promotion-title small {
  color: #718096;
  font-size: 0.85em;
  line-height: 1.3;
}

.type-badge {
  background: #e2e8f0;
  color: #4a5568;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8em;
  font-weight: 500;
}

.date-range div {
  font-size: 0.9em;
  margin-bottom: 2px;
}

.date-range div:first-child {
  color: #48bb78;
  font-weight: 500;
}

.date-range div:last-child {
  color: #e53e3e;
  font-weight: 500;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 0.8em;
  font-weight: 600;
}

.status-badge.active {
  background: #c6f6d5;
  color: #22543d;
}

.status-badge.inactive {
  background: #fed7d7;
  color: #742a2a;
}

.activity-indicator {
  font-size: 0.85em;
  font-weight: 500;
}

.activity-indicator.active {
  color: #22543d;
}

.activity-indicator.inactive {
  color: #742a2a;
}

.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.edit-btn, .toggle-btn, .delete-btn {
  padding: 6px 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1em;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.edit-btn {
  background: #bee3f8;
  color: #2b6cb0;
}

.edit-btn:hover {
  background: #90cdf4;
}

.toggle-btn {
  background: #d6f5d6;
  color: #22543d;
}

.toggle-btn:hover {
  background: #c6f6d5;
}

.toggle-btn.inactive {
  background: #fed7d7;
  color: #742a2a;
}

.toggle-btn.inactive:hover {
  background: #fbb6ce;
}

.delete-btn {
  background: #fed7d7;
  color: #742a2a;
}

.delete-btn:hover {
  background: #fbb6ce;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #4a5568;
}

.empty-icon {
  font-size: 4em;
  margin-bottom: 20px;
}

.empty-state h3 {
  margin-bottom: 10px;
  color: #2d3748;
}

.empty-state p {
  margin-bottom: 30px;
  color: #718096;
}

/* Responsive */
@media (max-width: 1200px) {
  .promotions-table-container {
    overflow-x: auto;
  }
  
  .promotions-table {
    min-width: 800px;
  }
}

@media (max-width: 768px) {
  .promotion-management {
    padding: 15px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .page-header h1 {
    font-size: 1.5em;
    text-align: center;
  }
  
  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }
  
  .stat-card {
    padding: 15px;
  }
  
  .stat-card h3 {
    font-size: 1.5em;
  }
  
  .promotions-table th,
  .promotions-table td {
    padding: 10px 8px;
    font-size: 0.9em;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }
  
  .edit-btn, .toggle-btn, .delete-btn {
    padding: 4px 6px;
    font-size: 0.9em;
  }
}
